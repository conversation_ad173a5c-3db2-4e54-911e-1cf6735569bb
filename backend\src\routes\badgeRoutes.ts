import express from 'express';
import {
  getUserBadgesController,
  getUserPrimaryBadgeController,
  getPublicUserBadges,
  getPublicUserPrimaryBadge,
  checkUserBadges,
  getBadgeConfig,
  getBadgeStats,
  getUsersWithBadge,
  testBadgeSystem
} from '../controllers/badgeController';
import { verifyToken as protect } from '../middlewares/auth.middleware';

const router = express.Router();

// Public routes
router.get('/config', getBadgeConfig);
router.get('/test', testBadgeSystem);
router.get('/user/:userId', getPublicUserBadges);
router.get('/user/:userId/primary', getPublicUserPrimaryBadge);

// Protected routes
router.get('/my-badges', protect, getUserBadgesController);
router.get('/my-primary', protect, getUserPrimaryBadgeController);
router.post('/check', protect, checkUserBadges);

// Admin routes
router.get('/admin/stats', protect, getBadgeStats);
router.get('/admin/users', protect, getUsersWithBadge);

export default router;
