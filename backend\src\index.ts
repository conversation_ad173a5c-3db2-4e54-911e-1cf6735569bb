// Load environment variables FIRST
import dotenv from 'dotenv';
dotenv.config();

import express, { Request, Response, NextFunction, ErrorRequestHandler } from 'express';
import cors from 'cors';
import mongoose from 'mongoose';
import passport from 'passport';

// Import type definitions
import './types/express';
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
// import charityRoutes from './routes/charity.routes';
import donationRoutes from './routes/donationRoutes';
import homeRoutes from './routes/home.routes';

import campaignRoutes from './routes/campaignRoutes';
import testRoutes from './routes/test.routes';

console.log('🔧 [Server] Campaign routes imported:', typeof campaignRoutes);
import http from 'http';
import { Server as SocketIOServer } from 'socket.io';
import postsRoutes from './routes/posts.routes';
import notificationRoutes from './routes/notificationRoutes';
import adminRoutes from './routes/admin.routes';
import eventRoutes from './routes/event.routes';
import badgeRoutes from './routes/badgeRoutes';

// Import cron jobs
import { updateCampaignStatusJob } from './cron/updateCampaignStatus';
import { cleanupPendingDonations } from './cron/cleanupDonations';
import { startCampaignReminderJob } from './cron/campaignReminders';
import { startEventReminderJob } from './cron/eventReminders';
import { initActivityCleanup } from './utils/activity-tracker';

// Create Express app
const app = express();

// Create HTTP server with increased header size limit
const server = http.createServer({
  maxHeaderSize: 16384 // 16KB header limit (default is 8KB)
}, app);

// Create Socket.IO server
export const io = new SocketIOServer(server, {
  cors: {
    origin: ['http://localhost:5173', 'http://*********.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174'],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    credentials: true
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join user to their own room for personal notifications
  socket.on('join-user', (userId) => {
    if (userId) {
      socket.join(`user-${userId}`);
      console.log(`User ${userId} joined their room`);

      // Send confirmation
      socket.emit('joined-room', { userId, room: `user-${userId}` });
    }
  });

  // Handle authentication with token
  socket.on('authenticate', async (token) => {
    try {
      // Verify JWT token and extract user ID
      const jwt = await import('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;

      if (decoded && decoded.id) {
        socket.join(`user-${decoded.id}`);
        console.log(`Authenticated user ${decoded.id} joined room: user-${decoded.id}`);
        socket.emit('authenticated', { userId: decoded.id });
      }
    } catch (error) {
      console.error('Socket authentication failed:', error);
      socket.emit('auth-error', { message: 'Authentication failed' });
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Multer for handling multipart/form-data
import multer from 'multer';
multer({
  dest: 'uploads/',
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:5173', 'http://127.0.0.1:5173',
    'http://localhost:5174', 'http://127.0.0.1:5174',
    'http://localhost:5175', 'http://127.0.0.1:5175',
    'http://localhost:3000', 'http://127.0.0.1:3000'
  ],
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  optionsSuccessStatus: 200
}));

// Handle preflight requests explicitly
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

// Initialize Passport
app.use(passport.initialize());

// Middleware to attach Socket.IO to requests
app.use((req: Request, _res: Response, next: NextFunction) => {
  (req as any).io = io;
  next();
});

// Logging middleware
app.use((req: Request, _res: Response, next: NextFunction) => {
  console.log(`🌐 ${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Log header sizes to debug 431 errors
  const headerSize = JSON.stringify(req.headers).length;
  if (headerSize > 4000) { // Log if headers > 4KB
    console.warn(`⚠️ Large headers detected: ${headerSize} bytes`);
    console.log('📋 Headers:', Object.keys(req.headers));
    if (req.headers.authorization) {
      const authSize = req.headers.authorization.length;
      console.log(`🔑 Auth header size: ${authSize} bytes`);
    }
  }

  if (req.body && Object.keys(req.body).length > 0) {
    const bodySize = JSON.stringify(req.body).length;
    console.log(`📝 Request body size: ${bodySize} bytes`);
  }

  next();
});

// Routes
console.log('🔧 [Server] Mounting routes...');
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/donations', donationRoutes);
app.use('/api/home', homeRoutes);
console.log('🔧 [Server] Mounting campaign routes...');
app.use('/api/campaigns', campaignRoutes);
app.use('/api/posts', postsRoutes); // Posts routes with full functionality
app.use('/api/notifications', notificationRoutes); // Notification routes
app.use('/api/admin', adminRoutes);
app.use('/api/events', eventRoutes); // Event routes
app.use('/api/badges', badgeRoutes); // Badge routes
app.use('/api/test', testRoutes);
console.log('🔧 [Server] All routes mounted');

// Static folder for uploads
app.use('/uploads', express.static('uploads'));

// Error handling middleware
const errorHandler: ErrorRequestHandler = (err, _req, res, _next) => {
  console.error(err.stack);
  res.status(err.status || 500).json({
    message: err.message || 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? err : {}
  });
};

app.use(errorHandler);

// Database connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/keydyweb';

console.log('🚀 Starting server...');
console.log('📊 MongoDB URI:', MONGODB_URI ? 'Atlas URI configured' : 'Local MongoDB');

mongoose.connect(MONGODB_URI, {
  serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
  socketTimeoutMS: 45000,
})
  .then(() => {
    console.log('✅ Connected to MongoDB');

    // Start cron jobs
    console.log('⏰ Starting cron jobs...');
    updateCampaignStatusJob.start();
    cleanupPendingDonations.start();
    startCampaignReminderJob();
    startEventReminderJob();
    console.log('✅ Cron jobs started');

    // Initialize activity tracking cleanup
    initActivityCleanup();
    console.log('🟢 Activity tracking initialized');

    // Start server after DB connection
    const PORT = process.env.PORT || 5001;
    server.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
      console.log('🌐 CORS enabled for:', ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://localhost:5174', 'http://127.0.0.1:5174']);
      console.log('🔌 Socket.IO enabled');
      console.log('⏰ Cron jobs running:');
      console.log('  - Campaign status update (every hour)');
      console.log('  - Cleanup pending donations (every hour)');
      console.log('  - Campaign deadline reminders (daily at 9:00 AM)');
      console.log('  - Event reminders (hourly)');
      console.log('📋 Available routes:');
      console.log('  - /api/auth');
      console.log('  - /api/campaigns');
      console.log('  - /api/admin');
      console.log('  - /api/posts');
      console.log('  - /api/test');
    });
  })
  .catch((error) => {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  });

// Handle unhandled promise rejections
process.on('unhandledRejection', (error) => {
  console.error('Unhandled Promise Rejection:', error);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});