const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Connect to MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/keydyweb';
console.log('🔗 Using MongoDB URI:', MONGODB_URI ? 'Atlas URI configured' : 'Local MongoDB');

async function fixImages() {
  try {
    console.log('🔧 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get Campaign model
    const Campaign = mongoose.model('Campaign', new mongoose.Schema({
      title: String,
      images: [String]
    }, { collection: 'campaigns' }));

    // Get all campaigns
    const campaigns = await Campaign.find({});
    console.log(`📊 Found ${campaigns.length} campaigns to check`);

    // Get all files in uploads directory
    const uploadsDir = path.join(__dirname, 'uploads');
    const files = fs.readdirSync(uploadsDir).filter(file => 
      file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.gif')
    );
    console.log(`📁 Found ${files.length} image files in uploads:`, files);

    let fixedCount = 0;

    for (const campaign of campaigns) {
      console.log(`\n🔍 Checking campaign: ${campaign.title}`);
      console.log(`   Current images:`, campaign.images);
      
      let hasChanges = false;
      const newImages = [];

      for (const imagePath of campaign.images) {
        console.log(`   Processing image: ${imagePath}`);
        
        // Extract filename from path
        const filename = imagePath.split('/').pop() || imagePath;
        console.log(`   Extracted filename: ${filename}`);

        // Check if file exists in uploads
        if (files.includes(filename)) {
          newImages.push(`/uploads/${filename}`);
          console.log(`   ✅ Fixed: ${imagePath} -> /uploads/${filename}`);
          hasChanges = true;
        } else {
          // Try to find similar filename
          const similarFile = files.find(file => 
            file.includes(filename.split('-')[0]) || 
            filename.includes(file.split('-')[0])
          );

          if (similarFile) {
            newImages.push(`/uploads/${similarFile}`);
            console.log(`   🔄 Mapped: ${imagePath} -> /uploads/${similarFile}`);
            hasChanges = true;
          } else {
            console.log(`   ❌ No matching file found for: ${imagePath}`);
            // Don't include this image
          }
        }
      }

      if (hasChanges) {
        campaign.images = newImages;
        await campaign.save();
        fixedCount++;
        console.log(`   ✅ Updated campaign: ${campaign.title}`);
        console.log(`   New images:`, newImages);
      } else {
        console.log(`   ⏭️ No changes needed for: ${campaign.title}`);
      }
    }

    console.log(`\n🎉 Fixed ${fixedCount} campaigns out of ${campaigns.length} total`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

fixImages();
