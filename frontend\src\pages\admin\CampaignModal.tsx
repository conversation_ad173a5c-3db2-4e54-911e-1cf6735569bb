import React, { useState, useEffect } from 'react';
import { X, Upload, Calendar, DollarSign } from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001';

interface Campaign {
  _id?: string;
  title: string;
  description: string;
  targetAmount: number;
  startDate: string;
  endDate: string;
  category?: string;
  status: 'active' | 'completed' | 'cancelled' | 'draft';
  images: string[];
}

interface CampaignModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  campaign?: Campaign | null;
}

const CampaignModal: React.FC<CampaignModalProps> = ({ isOpen, onClose, onSave, campaign }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    targetAmount: '',
    startDate: '',
    endDate: '',
    category: '',
    status: 'draft' as 'active' | 'completed' | 'cancelled' | 'draft'
  });
  
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // Helper function to get correct image URL
  const getImageUrl = (imagePath: string) => {
    if (!imagePath) return '';

    console.log('🔍 [CampaignModal] Processing image path:', imagePath);

    // If it's already a full URL (starts with http), check if it's accessible
    if (imagePath.startsWith('http')) {
      // Extract the filename from the full URL
      const urlParts = imagePath.split('/');
      const filename = urlParts[urlParts.length - 1];

      // Try to construct a simpler path first
      const simplePath = `${API_URL}/uploads/${filename}`;
      console.log('🔍 [CampaignModal] Trying simple path:', simplePath);
      return simplePath;
    }

    // If it starts with /uploads, prepend API_URL
    if (imagePath.startsWith('/uploads')) {
      return `${API_URL}${imagePath}`;
    }

    // If it starts with uploads (without /), prepend API_URL with /
    if (imagePath.startsWith('uploads')) {
      return `${API_URL}/${imagePath}`;
    }

    // Default case: assume it's a relative path
    return `${API_URL}/uploads/${imagePath}`;
  };

  useEffect(() => {
    if (campaign) {
      // Edit mode
      setFormData({
        title: campaign.title,
        description: campaign.description,
        targetAmount: campaign.targetAmount.toString(),
        startDate: campaign.startDate.split('T')[0],
        endDate: campaign.endDate.split('T')[0],
        category: campaign.category || '',
        status: campaign.status
      });
      setExistingImages(campaign.images || []);
      setImagePreviewUrls(campaign.images?.map(img => getImageUrl(img)) || []);
    } else {
      // Create mode
      resetForm();
    }
  }, [campaign, isOpen]);

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      targetAmount: '',
      startDate: '',
      endDate: '',
      category: '',
      status: 'draft'
    });
    setSelectedImages([]);
    setImagePreviewUrls([]);
    setExistingImages([]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length + selectedImages.length + existingImages.length > 5) {
      toast.error('Tối đa 5 hình ảnh');
      return;
    }

    setSelectedImages(prev => [...prev, ...files]);
    
    // Create preview URLs
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviewUrls(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    const totalExisting = existingImages.length;
    
    if (index < totalExisting) {
      // Remove existing image
      setExistingImages(prev => prev.filter((_, i) => i !== index));
    } else {
      // Remove new image
      const newImageIndex = index - totalExisting;
      setSelectedImages(prev => prev.filter((_, i) => i !== newImageIndex));
    }
    
    setImagePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      toast.error('Vui lòng nhập tiêu đề chiến dịch');
      return false;
    }
    if (!formData.description.trim()) {
      toast.error('Vui lòng nhập mô tả chiến dịch');
      return false;
    }
    if (!formData.targetAmount || parseFloat(formData.targetAmount) < 10000) {
      toast.error('Số tiền mục tiêu phải ít nhất 10,000 VNĐ');
      return false;
    }
    if (!formData.startDate) {
      toast.error('Vui lòng chọn ngày bắt đầu');
      return false;
    }
    if (!formData.endDate) {
      toast.error('Vui lòng chọn ngày kết thúc');
      return false;
    }
    if (new Date(formData.endDate) <= new Date(formData.startDate)) {
      toast.error('Ngày kết thúc phải sau ngày bắt đầu');
      return false;
    }
    // Images are optional now
    // if (existingImages.length + selectedImages.length === 0) {
    //   toast.error('Vui lòng thêm ít nhất 1 hình ảnh');
    //   return false;
    // }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🎯 [CampaignModal] Form submitted');

    if (!validateForm()) {
      console.log('❌ [CampaignModal] Form validation failed');
      return;
    }

    console.log('✅ [CampaignModal] Form validation passed');
    setLoading(true);
    try {
      console.log('🚀 [CampaignModal] Starting form submission');
      console.log('📝 [CampaignModal] Form data:', formData);
      console.log('🖼️ [CampaignModal] Selected images:', selectedImages.length);
      console.log('🖼️ [CampaignModal] Existing images:', existingImages.length);
      console.log('🌐 [CampaignModal] API_URL:', API_URL);

      const token = localStorage.getItem('token');
      const formDataToSend = new FormData();
      
      formDataToSend.append('title', formData.title);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('targetAmount', formData.targetAmount);
      formDataToSend.append('startDate', formData.startDate);
      formDataToSend.append('endDate', formData.endDate);
      formDataToSend.append('category', formData.category || 'Khác');
      // Note: status is set automatically by backend, don't send it

      // Add existing images (only for updates)
      if (campaign?._id) {
        existingImages.forEach(image => {
          formDataToSend.append('existingImages', image);
        });
      }

      // Add new images
      selectedImages.forEach(image => {
        formDataToSend.append('images', image);
      });

      if (campaign?._id) {
        // Update campaign
        console.log('🔄 [CampaignModal] Updating campaign with ID:', campaign._id);
        const response = await axios.put(`${API_URL}/api/campaigns/admin/${campaign._id}`, formDataToSend, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          }
        });
        console.log('✅ [CampaignModal] Update response:', response.data);
        toast.success('Cập nhật chiến dịch thành công');
      } else {
        // Create campaign
        console.log('🆕 [CampaignModal] Creating new campaign');
        console.log('🔗 [CampaignModal] Request URL:', `${API_URL}/api/campaigns/admin`);
        console.log('🔑 [CampaignModal] Token:', token ? 'Present' : 'Missing');

        const response = await axios.post(`${API_URL}/api/campaigns/admin`, formDataToSend, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          }
        });
        console.log('✅ [CampaignModal] Create response:', response.data);
        toast.success('Tạo chiến dịch thành công');
      }
      
      onSave();
      onClose();
    } catch (error: any) {
      console.error('Error saving campaign:', error);
      toast.error(error.response?.data?.message || 'Có lỗi xảy ra khi lưu chiến dịch');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {campaign ? 'Chỉnh sửa chiến dịch' : 'Tạo chiến dịch mới'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Tiêu đề chiến dịch *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Nhập tiêu đề chiến dịch..."
              required
            />
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Mô tả chiến dịch *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Nhập mô tả chi tiết về chiến dịch..."
              required
            />
          </div>

          {/* Target Amount */}
          <div>
            <label htmlFor="targetAmount" className="block text-sm font-medium text-gray-700 mb-2">
              Số tiền mục tiêu (VNĐ) *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="number"
                id="targetAmount"
                name="targetAmount"
                value={formData.targetAmount}
                onChange={handleInputChange}
                min="10000"
                step="10000"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="10,000"
                required
              />
            </div>
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              Danh mục
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Chọn danh mục</option>
              <option value="Giáo dục">Giáo dục</option>
              <option value="Y tế">Y tế</option>
              <option value="Môi trường">Môi trường</option>
              <option value="Trẻ em">Trẻ em</option>
              <option value="Người già">Người già</option>
              <option value="Động vật">Động vật</option>
              <option value="Cộng đồng">Cộng đồng</option>
              <option value="Khác">Khác</option>
            </select>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-2">
                Ngày bắt đầu *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-2">
                Ngày kết thúc *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>
          </div>

          {/* Status */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Trạng thái
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="draft">Bản nháp</option>
              <option value="active">Đang hoạt động</option>
              <option value="completed">Đã hoàn thành</option>
              <option value="cancelled">Đã hủy</option>
            </select>
          </div>

          {/* Images */}
          <div>
            <label htmlFor="image-upload" className="block text-sm font-medium text-gray-700 mb-2">
              Hình ảnh chiến dịch (Tối đa 5 ảnh)
            </label>

            {/* Image Upload */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
                id="image-upload"
                name="images"
              />
              <label htmlFor="image-upload" className="cursor-pointer">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">
                  Nhấp để chọn ảnh hoặc kéo thả ảnh vào đây
                </p>
                <p className="text-xs text-gray-500">PNG, JPG, GIF tối đa 10MB</p>
              </label>
            </div>

            {/* Image Previews */}
            {imagePreviewUrls.length > 0 && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
                {imagePreviewUrls.map((url, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={url}
                      alt={`Preview ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg border border-gray-200"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        console.error('❌ [CampaignModal] Failed to load image:', url);

                        // Try fallback URLs
                        const originalSrc = target.src;

                        // If it's a complex path, try to extract just the filename
                        if (originalSrc.includes('/uploads/') && originalSrc.includes('/images-')) {
                          const filename = originalSrc.split('/').pop();
                          const fallbackUrl = `${API_URL}/uploads/${filename}`;

                          if (originalSrc !== fallbackUrl) {
                            console.log('🔄 [CampaignModal] Trying fallback URL:', fallbackUrl);
                            target.src = fallbackUrl;
                            return;
                          }
                        }

                        // If all fails, hide the image
                        target.style.display = 'none';
                        console.error('❌ [CampaignModal] All fallback attempts failed for:', originalSrc);
                      }}
                      onLoad={() => {
                        console.log('✅ [CampaignModal] Image loaded successfully:', url);
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Đang lưu...' : (campaign ? 'Cập nhật' : 'Tạo chiến dịch')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CampaignModal;
