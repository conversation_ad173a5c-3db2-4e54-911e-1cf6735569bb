import express from 'express';
import {
  getUserNotifications,
  markAs<PERSON><PERSON>,
  markAll<PERSON><PERSON>ead,
  getUnreadCount,
  createAdminNotification,
  getNotificationStats,
  getAllNotifications,
  deleteNotification,
  createTestNotification
} from '../controllers/notificationController';
import { verifyToken as protect } from '../middlewares/auth.middleware';

const router = express.Router();

// Get unread notifications count (no auth required for polling)
router.get('/unread-count', protect, getUnreadCount);

// Apply protect middleware to routes that need authentication
// Get user's notifications
router.get('/', protect, getUserNotifications);

// Mark notification as read
router.patch('/:notificationId/read', protect, markAsRead);

// Mark all notifications as read
router.patch('/read-all', protect, markAllAsRead);

// Admin routes
router.post('/admin/create', protect, createAdminNotification);
router.get('/admin/stats', protect, getNotificationStats);
router.get('/admin/all', protect, getAllNotifications);
router.delete('/admin/:notificationId', protect, deleteNotification);

// Test route (for development)
router.post('/test', protect, createTestNotification);

export default router;